# Google Analytics 4 (GA4) Chatbot Tracking Setup

## Overview
This document explains how to track user interactions with the chatbot using Google Analytics 4. The chatbot sends custom events to GA4 when users make different choices.

## Prerequisites
Ensure your website has Google Analytics 4 (GA4) tracking code installed on all pages. The tracking code should include the `gtag.js` library.

## Events Being Tracked

### 1. Main Choice Events
When users choose between the two main options:

**Event Name:** `chatbot_choice`

#### Option A: Create CV (🚀 立即创建我的简历)
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Create CV',
  'selection': 'create_cv',
  'language': 'zh' // or 'en', 'de', 'es'
});
```

#### Option B: Chat First (💬 先聊聊了解更多)
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Chat First',
  'selection': 'chat_first',
  'language': 'zh' // or 'en', 'de', 'es'
});
```

### 2. Contact Method Events
When users choose how to connect:

#### WhatsApp Direct Contact
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'WhatsApp Direct',
  'selection': 'whatsapp_direct',
  'language': 'en'
});
```

#### Facebook Direct Contact
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Facebook Direct',
  'selection': 'facebook_direct',
  'language': 'en'
});
```

#### Leave Contact Info
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Leave Contact',
  'selection': 'leave_contact',
  'language': 'en'
});
```

#### Contact Info Submitted
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Contact Submitted (Phone)', // or 'Contact Submitted (Social)'
  'selection': 'contact_submitted',
  'language': 'en'
});
```

### 3. Chinese-Specific Events

#### WeChat Direct Contact (Chinese users only)
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'WhatsApp Direct (Chinese)',
  'selection': 'whatsapp_direct_zh',
  'language': 'zh'
});
```

#### Leave Contact Info (Chinese users)
```javascript
gtag('event', 'chatbot_choice', {
  'event_category': 'Chatbot Interaction',
  'event_label': 'Leave Contact (Chinese)',
  'selection': 'leave_contact_zh',
  'language': 'zh'
});
```

## How to View Data in GA4

### 1. Real-time Reports
- Go to GA4 → Reports → Realtime
- Look for "Event count by Event name"
- You should see `chatbot_choice` events appearing

### 2. Custom Reports
Create a custom report to analyze chatbot performance:

1. Go to GA4 → Explore → Create a new exploration
2. Add these dimensions:
   - Event name
   - Custom parameter: selection
   - Custom parameter: event_label
   - Custom parameter: language
3. Add these metrics:
   - Event count
   - Users

### 3. Conversion Tracking
Set up `create_cv` as a conversion event:
1. Go to GA4 → Configure → Events
2. Find the `chatbot_choice` event
3. Toggle "Mark as conversion" for events where `selection = 'create_cv'`

## Key Metrics to Monitor

### 1. Conversion Rate
- **Create CV Rate**: `create_cv` events / total initial chatbot interactions
- **Contact Rate**: Contact-related events / `chat_first` events

### 2. Language Performance
- Compare conversion rates across different languages
- Identify which languages have higher engagement

### 3. Contact Method Preferences
- WhatsApp vs Facebook vs Leave Info
- Phone vs Social media contact submissions

### 4. User Journey Analysis
Track the complete user flow:
1. Chatbot opens (auto-popup or manual)
2. Initial choice (CV vs Chat)
3. Contact method selection
4. Final action (redirect to form or contact submission)

## Sample GA4 Query (BigQuery)
If you have GA4 BigQuery export enabled:

```sql
SELECT
  event_name,
  event_timestamp,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'selection') as selection,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'event_label') as event_label,
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'language') as language,
  user_pseudo_id
FROM `your-project.analytics_XXXXXX.events_*`
WHERE event_name = 'chatbot_choice'
  AND _TABLE_SUFFIX BETWEEN '20241201' AND '20241231'
ORDER BY event_timestamp DESC
```

## Implementation Status
✅ **Implemented Events:**
- Main choice tracking (Create CV vs Chat First)
- Contact method tracking (WhatsApp, Facebook, Leave Info)
- Contact submission tracking
- Language-specific tracking
- Chinese-specific WeChat options

✅ **Event Parameters:**
- `event_category`: "Chatbot Interaction"
- `event_label`: Human-readable description
- `selection`: Machine-readable identifier
- `language`: User's current language setting

## Testing
To test the implementation:
1. Open browser developer tools
2. Go to Network tab
3. Interact with the chatbot
4. Look for requests to `google-analytics.com/g/collect`
5. Verify the event parameters are being sent correctly

## Next Steps
1. Set up GA4 dashboard for chatbot metrics
2. Create automated reports for weekly/monthly analysis
3. Set up alerts for significant changes in conversion rates
4. A/B test different chatbot messages based on the data
