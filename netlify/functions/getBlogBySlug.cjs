/**
 * Get Blog Post by Slug
 * Fetches a single blog post from Notion database by slug, including full content blocks
 */

const { Client } = require('@notionhq/client');

exports.handler = async (event, context) => {
  // Set CORS headers (same as getBlogs.cjs)
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
    'Cache-Control': 'public, max-age=300' // 5-minute cache
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Validate environment variables
    const notionToken = process.env.NOTION_TOKEN;
    const notionDatabaseId = process.env.NOTION_DATABASE_ID;

    if (!notionToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'NOTION_TOKEN environment variable is not configured.',
          blog: null
        })
      };
    }

    if (!notionDatabaseId) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'NOTION_DATABASE_ID environment variable is not configured.',
          blog: null
        })
      };
    }

    // Get slug from query parameters
    const queryParams = event.queryStringParameters || {};
    const slug = queryParams.slug;

    if (!slug) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Slug parameter is required.',
          blog: null
        })
      };
    }

    console.log(`Searching for blog post with slug: ${slug}`);

    // Initialize Notion client
    const notion = new Client({
      auth: notionToken,
    });

    // Query database to find page with matching slug
    const response = await notion.databases.query({
      database_id: notionDatabaseId,
      filter: {
        and: [
          {
            property: 'Published',
            checkbox: {
              equals: true
            }
          },
          {
            property: 'Slug',
            rich_text: {
              equals: slug
            }
          }
        ]
      }
    });

    console.log(`Found ${response.results.length} pages matching slug`);

    if (response.results.length === 0) {
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Blog post not found.',
          blog: null
        })
      };
    }

    const page = response.results[0];
    const properties = page.properties || {};

    // Extract page properties (same logic as getBlogs.cjs)
    const titleProperty = properties.Title;
    const title = titleProperty?.title?.[0]?.plain_text || 'Untitled';
    
    const excerptProperty = properties.Excerpt;
    const excerpt = excerptProperty?.rich_text?.[0]?.plain_text || '';
    
    const coverProperty = properties.Cover;
    let coverImage = '';
    if (coverProperty?.files?.[0]) {
      const file = coverProperty.files[0];
      coverImage = file.file?.url || file.external?.url || '';
    }
    
    const dateProperty = properties.Date;
    const publishedDate = dateProperty?.date?.start || new Date().toISOString();
    
    const slugProperty = properties.Slug;
    const pageSlug = slugProperty?.rich_text?.[0]?.plain_text || slug;
    
    const languageProperty = properties.Language;
    const language = languageProperty?.select?.name || 'en';
    
    const tagsProperty = properties.Tags;
    const tags = tagsProperty?.multi_select?.map(tag => tag.name) || [];

    console.log(`Fetching content blocks for page: ${page.id}`);

    // Fetch all content blocks from the page
    const blocksResponse = await notion.blocks.children.list({
      block_id: page.id,
      page_size: 100
    });

    console.log(`Found ${blocksResponse.results.length} content blocks`);

    // Transform blocks to simplified format
    const content = blocksResponse.results.map(block => {
      const baseBlock = {
        id: block.id,
        type: block.type
      };

      switch (block.type) {
        case 'paragraph':
          return {
            ...baseBlock,
            text: block.paragraph?.rich_text?.[0]?.plain_text || ''
          };
        
        case 'heading_1':
          return {
            ...baseBlock,
            text: block.heading_1?.rich_text?.[0]?.plain_text || '',
            level: 1
          };
        
        case 'heading_2':
          return {
            ...baseBlock,
            text: block.heading_2?.rich_text?.[0]?.plain_text || '',
            level: 2
          };
        
        case 'heading_3':
          return {
            ...baseBlock,
            text: block.heading_3?.rich_text?.[0]?.plain_text || '',
            level: 3
          };
        
        case 'bulleted_list_item':
          return {
            ...baseBlock,
            text: block.bulleted_list_item?.rich_text?.[0]?.plain_text || ''
          };
        
        case 'numbered_list_item':
          return {
            ...baseBlock,
            text: block.numbered_list_item?.rich_text?.[0]?.plain_text || ''
          };
        
        default:
          // For unsupported block types, return basic info
          return {
            ...baseBlock,
            text: `[Unsupported block type: ${block.type}]`
          };
      }
    });

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        blog: {
          id: page.id,
          title,
          excerpt,
          slug: pageSlug,
          tags,
          language,
          publishedDate,
          coverImage,
          content
        }
      })
    };

  } catch (error) {
    console.error('Error in getBlogBySlug function:', error);
    
    // Handle specific Notion API errors
    let errorMessage = 'Internal server error occurred while fetching blog post.';
    let statusCode = 500;
    
    if (error.code === 'unauthorized') {
      errorMessage = 'Authentication failed: Invalid Notion token.';
      statusCode = 401;
    } else if (error.code === 'object_not_found') {
      errorMessage = 'Notion database not found or not accessible.';
      statusCode = 404;
    } else if (error.code === 'rate_limited') {
      errorMessage = 'Rate limit exceeded. Please try again later.';
      statusCode = 429;
    } else if (error.code === 'invalid_request') {
      errorMessage = 'Invalid request to Notion API.';
      statusCode = 400;
    } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = 'Network error: Unable to connect to Notion API.';
      statusCode = 503;
    }
    
    // Return error response with guaranteed JSON structure
    return {
      statusCode,
      headers,
      body: JSON.stringify({
        success: false,
        error: errorMessage,
        blog: null,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};
