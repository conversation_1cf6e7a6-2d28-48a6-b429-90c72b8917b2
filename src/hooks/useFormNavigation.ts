import { useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useFormTab, TabType } from '@/contexts/FormTabContext';

export const useFormNavigation = () => {
  const location = useLocation();
  const { setPreferredTab, getDefaultTabForPage } = useFormTab();

  const scrollToFormSection = useCallback((targetTab?: TabType) => {
    // Find the form section element
    const formSection = document.querySelector('[data-form-section]');

    if (formSection) {
      // Determine which tab to show
      const tabToShow = targetTab || getDefaultTabForPage();

      // Set the preferred tab in context (this will trigger the FormSection to switch)
      setPreferredTab(tabToShow);

      // Smooth scroll to the form section
      formSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });

      // Focus the first input in the active form after scroll and tab switch
      setTimeout(() => {
        const activeTabContent = document.querySelector(`[data-state="active"]`);
        if (activeTabContent) {
          const firstInput = activeTabContent.querySelector('input, select, textarea');
          if (firstInput && firstInput instanceof HTMLElement) {
            firstInput.focus();
          }
        }
      }, 1000); // Wait for scroll and tab switch animations to complete
    }
  }, [setPreferredTab, getDefaultTabForPage]);

  return { scrollToFormSection };
};
