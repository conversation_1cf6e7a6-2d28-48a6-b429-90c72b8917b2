import React, { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { Calendar, Tag, Loader2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useI18n } from '../contexts/I18nContext';
import { useDesignTokens } from '../hooks/useDesignTokens';
import { useNavigation } from '../contexts/NavigationContext';
import { useTheme } from '../contexts/ThemeContext';
import { useAssets } from '../hooks/useAssets';
import { BlogPost } from '../types/blog';
import BlogHeader from '../components/BlogHeader';
import Footer from '../components/Footer';

interface BlogApiResponse {
  posts: BlogPost[];
  total: number;
  success: boolean;
  error?: string;
}

interface TagsApiResponse {
  tags: string[];
  total: number;
  success: boolean;
  error?: string;
}

const BlogListPage: React.FC = () => {
  const { t, language } = useI18n();
  const { tokens, combineClasses, themeClasses } = useDesignTokens();
  const { markInternalNavigation } = useNavigation();
  const { currentTheme, isEmployerTheme } = useTheme();
  const { ui } = useAssets();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [tagsLoading, setTagsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tagsError, setTagsError] = useState<string | null>(null);

  // Determine audience based on URL parameter or current theme
  const urlAudience = searchParams.get('audience');
  const audience = urlAudience || (currentTheme === 'employer' ? 'Employer' : 'Job-seeker');



  // Format date according to current language locale
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const localeMap: Record<string, string> = {
        'en': 'en-US',
        'de': 'de-DE',
        'zh': 'zh-CN',
        'es': 'es-ES'
      };
      
      return date.toLocaleDateString(localeMap[language] || 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return dateString;
    }
  };

  // Fetch blog posts from the API
  const fetchBlogPosts = async () => {
    setLoading(true);
    setError(null);

    try {
      const apiUrl = `/.netlify/functions/getBlogs?audience=${encodeURIComponent(audience)}`;
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: BlogApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch blog posts');
      }
      
      // Filter posts by current language and sort by date
      const languageFilteredPosts = data.posts
        .filter(post => post.language === language)
        .sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());
      
      setPosts(languageFilteredPosts);
    } catch (err) {
      console.error('Error fetching blog posts:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Fetch available tags from the API
  const fetchBlogTags = async () => {
    setTagsLoading(true);
    setTagsError(null);

    try {
      const apiUrl = `/.netlify/functions/getBlogTags?audience=${encodeURIComponent(audience)}&language=${encodeURIComponent(language)}`;
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: TagsApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch blog tags');
      }
      
      setAllTags(data.tags || []);
    } catch (err) {
      console.error('Error fetching blog tags:', err);
      setTagsError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setTagsLoading(false);
    }
  };

  // Filter posts based on selected tags
  useEffect(() => {
    if (selectedTags.length === 0) {
      setFilteredPosts(posts);
    } else {
      const filtered = posts.filter(post => 
        selectedTags.some(selectedTag => 
          post.tags.includes(selectedTag)
        )
      );
      setFilteredPosts(filtered);
    }
  }, [posts, selectedTags]);

  // Fetch data when component mounts, language changes, or audience changes
  useEffect(() => {
    fetchBlogPosts();
  }, [language, audience]);

  // Fetch tags when component mounts, audience changes, or language changes
  useEffect(() => {
    fetchBlogTags();
  }, [audience, language]);

  // Clear selected tags when language changes to avoid showing invalid filters
  useEffect(() => {
    if (selectedTags.length > 0) {
      console.log('Language changed, clearing selected tags');
      setSelectedTags([]);
    }
  }, [language]);

  // Clear selected tags when audience changes to prevent invalid filter states
  useEffect(() => {
    if (selectedTags.length > 0) {
      console.log('Audience changed, clearing selected tags');
      setSelectedTags([]);
    }
  }, [audience]);

  // Handle tag selection
  const handleTagClick = (tag: string) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };

  // Clear all selected tags
  const clearFilters = () => {
    setSelectedTags([]);
  };

  // Handle navigation to blog post
  const handlePostClick = () => {
    markInternalNavigation('internal');
  };

  // Get posts count text
  const getPostsCountText = (count: number) => {
    if (count === 1) {
      return t('blog.posts_count_singular');
    }
    return t('blog.posts_count').replace('{count}', count.toString());
  };

  // Get theme-appropriate placeholder image
  const getPlaceholderImage = () => {
    // Use theme-specific placeholder or fallback to generic one
    return isEmployerTheme
      ? '/assets/images/blog-placeholder-employer.svg'
      : '/assets/images/blog-placeholder-job-seeker.svg';
  };

  // Handle image error with placeholder fallback
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    if (img.src !== getPlaceholderImage()) {
      img.src = getPlaceholderImage();
    }
  };

  return (
    <div className={combineClasses(
      "min-h-screen flex flex-col bg-background",
      isEmployerTheme ? "employer-theme" : "job-seeker-theme"
    )}>
      <BlogHeader />

      <main className="flex-1 container mx-auto px-4 py-8 pt-24">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {t('blog.all_posts')}
          </h1>
          {!loading && (
            <p className="text-muted-foreground">
              {getPostsCountText(filteredPosts.length)}
            </p>
          )}
        </div>

        {/* Tag Filter Section - Only show if there are posts and tags available */}
        {!loading && posts.length > 0 && allTags.length > 0 && (
          <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <h2 className="text-lg font-semibold text-foreground">
              {t('blog.filter_by_tags')}
            </h2>
            {selectedTags.length > 0 && (
              <button
                onClick={clearFilters}
                className={combineClasses(
                  "text-sm hover:text-primary transition-colors",
                  "flex items-center gap-1"
                )}
              >
                <X className="h-4 w-4" />
                {t('blog.clear_filters')}
              </button>
            )}
          </div>

          {/* Tags Loading State */}
          {tagsLoading && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span>{t('blog.loading_tags')}</span>
            </div>
          )}

          {/* Tags Error State */}
          {tagsError && (
            <p className="text-red-600 text-sm">{t('blog.error')}</p>
          )}

          {/* Tags Display */}
          {!tagsLoading && !tagsError && (
            <div className="flex flex-wrap gap-2">
              {allTags.length === 0 ? (
                <p className="text-muted-foreground text-sm">{t('blog.no_tags')}</p>
              ) : (
                allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => handleTagClick(tag)}
                    className={combineClasses(
                      "px-3 py-1 rounded-full text-sm transition-all duration-200",
                      "border border-brand-blue",
                      selectedTags.includes(tag)
                        ? "bg-primary text-primary-foreground border-primary"
                        : "bg-background text-foreground hover:bg-muted"
                    )}
                  >
                    {tag}
                  </button>
                ))
              )}
            </div>
          )}

          {/* Selected Tags Display */}
          {selectedTags.length > 0 && (
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground mb-2">
                {t('blog.selected_tags')}:
              </p>
              <div className="flex flex-wrap gap-2">
                {selectedTags.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-primary text-primary-foreground rounded text-sm"
                  >
                    {tag}
                    <button
                      onClick={() => handleTagClick(tag)}
                      className="hover:bg-primary/80 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
          </div>
        )}

        {/* Blog Posts Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
              <p className="text-muted-foreground">{t('blog.loading')}</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{t('blog.error')}</p>
          </div>
        ) : filteredPosts.length === 0 ? (
          <div className="text-center py-12">
            {selectedTags.length > 0 ? (
              <div>
                <p className="text-muted-foreground mb-2">{t('blog.no_posts_for_tags')}</p>
                <p className="text-sm text-muted-foreground mb-4">{t('blog.try_other_tags')}</p>
                <button
                  onClick={clearFilters}
                  className={combineClasses(
                    tokens.button.secondary,
                    "px-4 py-2 rounded-lg transition-colors"
                  )}
                >
                  {t('blog.clear_filters')}
                </button>
              </div>
            ) : posts.length === 0 ? (
              <div>
                <p className="text-muted-foreground mb-2">{t('blog.no_posts_audience_language')}</p>
                <p className="text-sm text-muted-foreground">{t('blog.try_different_language')}</p>
              </div>
            ) : (
              <p className="text-muted-foreground">{t('blog.no_posts')}</p>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <Link
                key={post.id}
                to={`/blog/${post.slug}`}
                onClick={handlePostClick}
                className={combineClasses(
                  tokens.card.elevated,
                  "group border-1 border-brand-blue hover:border-primary hover:shadow-2xl hover:-translate-y-1 hover:bg-muted/20 hover:ring-2 hover:ring-primary/40 rounded-lg box-border transition-all duration-300 overflow-hidden block"
                )}
              >
                {/* Cover Image with Placeholder Support */}
                <div className="aspect-video overflow-hidden bg-muted/20">
                  <img
                    src={post.coverImage || getPlaceholderImage()}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                    onError={handleImageError}
                  />
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Publication Date */}
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                    <Calendar className="h-4 w-4" />
                    <time dateTime={post.publishedDate}>
                      {t('blog.published_on')} {formatDate(post.publishedDate)}
                    </time>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold text-foreground mb-3 line-clamp-2 group-hover:font-bold">
                    {post.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-muted-foreground mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Tags */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex items-center gap-1 flex-wrap">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <div className="flex flex-wrap gap-1">
                        {post.tags.slice(0, 3).map(tag => (
                          <span
                            key={tag}
                            className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded"
                          >
                            {tag}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="text-xs text-muted-foreground">
                            +{post.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default BlogListPage;
