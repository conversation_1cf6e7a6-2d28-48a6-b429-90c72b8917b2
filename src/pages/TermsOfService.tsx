const TermsOfService = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="relative z-10 w-full border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 md:h-20">
            <div className="flex-shrink-0">
              <button
                onClick={() => window.location.href = '/'}
                className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
                aria-label="Go to homepage"
              >
                <img
                  src="/logo.png"
                  alt="Sohnus Logo"
                  className="h-8 md:h-10 w-auto"
                />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="prose prose-lg max-w-none">
          <h1 className="text-4xl font-bold text-foreground mb-6">Terms of Service</h1>
          <p className="text-muted-foreground mb-8"><strong>Effective Date:</strong> July 9, 2025</p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">1. Agreement to Terms</h2>
            <p className="text-muted-foreground leading-relaxed">
              By accessing or using the website and services provided by Sohnus ("we," "us," or "our") at sohnus.de ("Service"), you agree to be bound by these Terms of Service ("Terms"). If you disagree with any part of the terms, you may not access the Service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">2. Description of Service</h2>
            <p className="text-muted-foreground leading-relaxed">
              Our Service provides a platform to connect job seekers with employers. We collect information through forms from job seekers ("Worker Users") to generate resumes and from employers ("Company Users") to understand their hiring needs. The primary purpose is to facilitate introductions between these two parties.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">3. User Obligations</h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              By using this Service, you agree to the following:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
              <li>You will provide information that is accurate, complete, and current at all times.</li>
              <li>You will not use the Service for any unlawful or prohibited purpose.</li>
              <li><strong>For Job Seekers:</strong> You understand and agree that the information you provide will be processed to create a resume and will be shared with potential employers for recruitment purposes.</li>
              <li><strong>For Employers:</strong> You agree to use the candidate information shared with you solely for the purpose of evaluating candidates for employment within your organization and in compliance with all applicable laws.</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">4. Disclaimers</h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              The Service is provided on an "AS IS" and "AS AVAILABLE" basis. We make no warranty and disclaim all responsibility and liability for:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
              <li>The completeness, accuracy, or reliability of the Service.</li>
              <li>Any harm to your computer system, loss of data, or other harm that results from your access to or use of the Service.</li>
              <li>We do not guarantee employment for any Job Seeker, nor do we guarantee that an Employer will find suitable candidates. We are a platform for introduction and are not a party to any agreement or communication between users.</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">5. Limitation of Liability</h2>
            <p className="text-muted-foreground leading-relaxed">
              In no event shall Sohnus, nor its directors, employees, partners, or agents, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">6. Governing Law</h2>
            <p className="text-muted-foreground leading-relaxed">
              These Terms shall be governed and construed in accordance with the laws of Germany, without regard to its conflict of law provisions.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">7. Changes to Terms</h2>
            <p className="text-muted-foreground leading-relaxed">
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. We will provide notice of any changes by posting the new Terms of Service on this page. Your continued use of the Service after any such changes constitutes your acceptance of the new Terms.
            </p>
          </section>


        </div>
      </div>
    </div>
  );
};

export default TermsOfService;
