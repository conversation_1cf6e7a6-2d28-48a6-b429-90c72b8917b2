import React, { createContext, useContext, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export type ThemeVariant = 'job-seeker' | 'employer';

interface ThemeContextType {
  currentTheme: ThemeVariant;
  isJobSeekerTheme: boolean;
  isEmployerTheme: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const location = useLocation();

  // Determine theme based on current route and URL parameters
  const getCurrentTheme = (): ThemeVariant => {
    // For hiring page, always use employer theme
    if (location.pathname === '/hiring') {
      return 'employer';
    }

    // For blog pages, check audience parameter
    if (location.pathname.startsWith('/blog')) {
      const searchParams = new URLSearchParams(location.search);
      const audience = searchParams.get('audience');
      return audience === 'Employer' ? 'employer' : 'job-seeker';
    }

    // Default to job-seeker theme for all other pages
    return 'job-seeker';
  };

  const currentTheme = getCurrentTheme();
  const isJobSeekerTheme = currentTheme === 'job-seeker';
  const isEmployerTheme = currentTheme === 'employer';

  // Apply theme class to document body
  useEffect(() => {
    const body = document.body;

    // Remove existing theme classes
    body.classList.remove('job-seeker-theme', 'employer-theme');

    // Add current theme class
    if (isEmployerTheme) {
      body.classList.add('employer-theme');
    } else {
      body.classList.add('job-seeker-theme');
    }

    // Cleanup on unmount
    return () => {
      body.classList.remove('job-seeker-theme', 'employer-theme');
    };
  }, [currentTheme, isEmployerTheme, location.pathname, location.search]);

  const value: ThemeContextType = {
    currentTheme,
    isJobSeekerTheme,
    isEmployerTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
