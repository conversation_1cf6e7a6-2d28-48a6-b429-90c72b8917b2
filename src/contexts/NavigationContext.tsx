import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

export type NavigationSource = 'external' | 'internal' | 'modal' | 'header-toggle';

interface NavigationContextType {
  navigationSource: NavigationSource;
  markInternalNavigation: (source: NavigationSource) => void;
  shouldShowModal: () => boolean;
  resetNavigationSource: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

interface NavigationProviderProps {
  children: React.ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const [navigationSource, setNavigationSource] = useState<NavigationSource>('external');
  const [hasNavigatedInternally, setHasNavigatedInternally] = useState(false);
  const location = useLocation();

  // Track if this is the initial page load or a refresh
  useEffect(() => {
    const isInitialLoad = !hasNavigatedInternally;
    
    if (isInitialLoad) {
      // Check if this is a fresh page load/refresh vs internal navigation
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      const navigationType = navigationEntries[0]?.type;
      
      // Set source based on how user arrived
      if (navigationType === 'reload' || navigationType === 'navigate') {
        setNavigationSource('external');
      } else {
        // This might be internal navigation, but we'll be conservative and show modal
        setNavigationSource('external');
      }
    }
  }, [location.pathname, hasNavigatedInternally]);

  const markInternalNavigation = useCallback((source: NavigationSource) => {
    setNavigationSource(source);
    setHasNavigatedInternally(true);

    // Store in sessionStorage to persist across page loads within the session
    sessionStorage.setItem('sohnus-internal-nav', 'true');
    sessionStorage.setItem('sohnus-nav-source', source);
    sessionStorage.setItem('sohnus-nav-timestamp', Date.now().toString());
  }, []);

  const shouldShowModal = useCallback(() => {
    // Check sessionStorage for recent internal navigation
    const hasInternalNav = sessionStorage.getItem('sohnus-internal-nav') === 'true';
    const storedSource = sessionStorage.getItem('sohnus-nav-source') as NavigationSource;
    const timestamp = sessionStorage.getItem('sohnus-nav-timestamp');

    // Check if the internal navigation flag is recent (within 10 seconds)
    if (hasInternalNav && storedSource && timestamp) {
      const navTime = parseInt(timestamp);
      const now = Date.now();
      const timeDiff = now - navTime;

      // If navigation was recent and internal, don't show modal
      if (timeDiff < 10000 && (storedSource === 'internal' || storedSource === 'modal' || storedSource === 'header-toggle')) {
        return false;
      }
    }

    // Show modal for external navigation or first visit
    return navigationSource === 'external' || !hasNavigatedInternally;
  }, [navigationSource, hasNavigatedInternally]);

  const resetNavigationSource = useCallback(() => {
    setNavigationSource('external');
    setHasNavigatedInternally(false);
    sessionStorage.removeItem('sohnus-internal-nav');
    sessionStorage.removeItem('sohnus-nav-source');
    sessionStorage.removeItem('sohnus-nav-timestamp');
  }, []);

  // Clear internal navigation flag after a delay to allow modal on fresh visits
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only clear if we're not actively navigating
      if (navigationSource !== 'external') {
        sessionStorage.removeItem('sohnus-internal-nav');
        sessionStorage.removeItem('sohnus-nav-source');
        sessionStorage.removeItem('sohnus-nav-timestamp');
      }
    }, 15000); // Clear after 15 seconds of inactivity

    return () => clearTimeout(timer);
  }, [location.pathname, navigationSource]);

  const value: NavigationContextType = {
    navigationSource,
    markInternalNavigation,
    shouldShowModal,
    resetNavigationSource,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};
