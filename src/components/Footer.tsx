import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Mail } from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useI18n } from "@/contexts/I18nContext";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import { useTheme } from "@/contexts/ThemeContext";
import { useAssets } from "@/hooks/useAssets";
import { useNavigation } from "@/contexts/NavigationContext";
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';


const Footer = () => {
  const { t } = useI18n();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { scrollToFormSection } = useFormNavigation();
  const { currentTheme, isEmployerTheme } = useTheme();
  const { getLogo } = useAssets();
  const { markInternalNavigation } = useNavigation();

  // Determine current audience from URL params or theme context (for blog pages)
  const urlAudience = searchParams.get('audience');
  const currentAudience = urlAudience || (currentTheme === 'employer' ? 'Employer' : 'Job-seeker');
  const isEmployerAudience = currentAudience === 'Employer';

  // Get appropriate logo based on current theme/audience
  const logoSrc = isEmployerTheme ? getLogo('dark') : getLogo('main');

  // Handle Apply Now button - navigate to main page and scroll to worker form
  const handleApplyNowClick = () => {
    markInternalNavigation('internal');
    navigate('/');
    // Scroll to form section and set worker tab
    setTimeout(() => {
      scrollToFormSection('worker');
    }, 100);
  };

  // Handle Post Jobs button - navigate to hiring page and scroll to company form
  const handlePostJobsClick = () => {
    markInternalNavigation('internal');
    navigate('/hiring');
    // Scroll to form section and set company tab
    setTimeout(() => {
      scrollToFormSection('company');
    }, 100);
  };
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center">
                  <img src={logoSrc} alt="Sohnus Logo" className="h-8 w-auto" />
                </div>
              </div>
              <p className="text-primary-foreground leading-relaxed">
                {t('footer.company_description')}
              </p>
              {<div className="flex gap-3">
                <Button variant="ghost" size="icon" className="hover:bg-secondary hover:text-secondary-foreground">
                  <FaFacebook className="h-4 w-4" />
                </Button>
                
                <Button variant="ghost" size="icon" className="hover:bg-secondary hover:text-secondary-foreground">
                  <FaLinkedin className="h-4 w-4" />
                </Button>
                
              </div> }
            </div>

            {/* For Job Seekers */}
            <div>
              <h3 className="font-semibold text-lg mb-4">{t('footer.job_seekers')}</h3>
              <ul className="space-y-3">
                <li>
                  <Button
                    variant="ghost_invert"
                    onClick={handleApplyNowClick}
                    aria-label={t('footer.apply_now')}
                  >
                    {t('footer.apply_now')}
                  </Button>
                </li>
                {/* <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    How It Works
                  </Button>
                </li>
                <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    Success Stories
                  </Button>
                </li>
                <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    Career Resources
                  </Button>
                </li> */}
              </ul>
            </div>

            {/* For Employers */}
            <div>
              <h3 className="font-semibold text-lg mb-4">{t('footer.employers')}</h3>
              <ul className="space-y-3">
                <li>
                  <Button
                    variant="ghost_invert"
                    onClick={handlePostJobsClick}
                    aria-label={t('footer.post_jobs')}
                  >
                    {t('footer.post_jobs')}
                  </Button>
                </li>
                {/* <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    Find Talent
                  </Button>
                </li>
                <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    Pricing
                  </Button>
                </li>
                <li>
                  <Button variant="ghost" className="h-auto p-0 hover:text-primary justify-start">
                    Enterprise Solutions
                  </Button>
                </li> */}
              </ul>
            </div>

            {/* Contact & Legal */}
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-lg mb-4">{t('footer.contact_info')}</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-primary-foreground" />
                    <a
                      href="mailto:<EMAIL>"
                      className="text-sm text-primary-foreground hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-lg mb-4">{t('footer.legal')}</h3>
                <ul className="space-y-3">
                  <li>
                    <Button
                      variant="ghost_invert"
                      onClick={() => window.location.href = '/privacy-policy'}
                    >
                      {t('footer.privacy_policy')}
                    </Button>
                  </li>
                  <li>
                    <Button
                      variant="ghost_invert"
                      onClick={() => window.location.href = '/terms-of-service'}
                    >
                      {t('footer.terms_of_service')}
                    </Button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <Separator className="bg-secondary-foreground/20" />
        
        {/* Bottom Footer */}
        <div className="py-8">
          <div className="flex justify-center items-center">
            <div className="text-sm text-primary-foreground">
              {t('footer.copyright')}
            </div>
          </div>
        </div>
      </div>


    </footer>
  );
};

export default Footer;