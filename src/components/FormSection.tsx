import { useState, useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { FileText, ChevronDown } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useFormTab, TabType } from "@/contexts/FormTabContext";
import { useTheme } from "@/contexts/ThemeContext";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import WorkerFormSection from "./WorkerFormSection";
import CompanyFormSection from "./CompanyFormSection";

const FormSection = () => {
  const { t } = useI18n();
  const { preferredTab, getDefaultTabForPage, clearPreferredTab } = useFormTab();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<TabType>("worker");
  const [isFormExpanded, setIsFormExpanded] = useState(false);
  const isInitialMount = useRef(true);

  // Determine which form to show based on current route
  const isHiringPage = location.pathname === '/hiring';
  const showWorkerForm = !isHiringPage;
  const showCompanyForm = isHiringPage;
  const currentFormType: TabType = isHiringPage ? "company" : "worker";

  // Set initial tab based on page on first mount and expand appropriate form
  useEffect(() => {
    if (isInitialMount.current) {
      const defaultTab = getDefaultTabForPage();
      setActiveTab(defaultTab);

      // Expand the form if it matches the current page context
      if (defaultTab === currentFormType) {
        setIsFormExpanded(true);
      }

      isInitialMount.current = false;
    }
  }, [getDefaultTabForPage, currentFormType]);

  // Handle preferred tab changes (from navigation actions)
  useEffect(() => {
    if (preferredTab && !isInitialMount.current) {
      // Always respect programmatic navigation
      setActiveTab(preferredTab);

      // Expand the form if it matches the preferred tab and current page context
      if (preferredTab === currentFormType) {
        setIsFormExpanded(true);
      }

      // Clear the preferred tab after applying it
      setTimeout(() => {
        clearPreferredTab();
      }, 100);
    }
  }, [preferredTab, clearPreferredTab, currentFormType]);

  // Only the audience-relevant form is rendered based on current route

  // Handle keyboard navigation for accessibility
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setIsFormExpanded(!isFormExpanded);
    }
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30" data-form-section>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center">
              <FileText className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('forms.title')}
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Conditionally render only the audience-relevant form */}
          {showWorkerForm && (
            <Collapsible
              open={isFormExpanded}
              onOpenChange={setIsFormExpanded}
              className="w-full"
            >
              <CollapsibleTrigger
                className="w-full group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
                aria-expanded={isFormExpanded}
                aria-controls="worker-form-content"
                onKeyDown={handleKeyDown}
              >
                <div className="flex items-center justify-between p-6 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors duration-200 cursor-pointer touch-manipulation min-h-[60px]">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <div className="text-left">
                      <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                        {t('worker_form.title')}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {t('worker_form.subtitle')}
                      </p>
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-6 w-6 text-muted-foreground transition-transform duration-200 ${
                      isFormExpanded ? 'rotate-180' : ''
                    }`}
                    aria-hidden="true"
                  />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent
                id="worker-form-content"
                className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              >
                <div className="pt-4">
                  <WorkerFormSection />
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}

          {showCompanyForm && (
            <Collapsible
              open={isFormExpanded}
              onOpenChange={setIsFormExpanded}
              className="w-full"
            >
              <CollapsibleTrigger
                className="w-full group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
                aria-expanded={isFormExpanded}
                aria-controls="company-form-content"
                onKeyDown={handleKeyDown}
              >
                <div className="flex items-center justify-between p-6 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors duration-200 cursor-pointer touch-manipulation min-h-[60px]">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <div className="text-left">
                      <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                        {t('company_form.title')}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {t('company_form.subtitle')}
                      </p>
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-6 w-6 text-muted-foreground transition-transform duration-200 ${
                      isFormExpanded ? 'rotate-180' : ''
                    }`}
                    aria-hidden="true"
                  />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent
                id="company-form-content"
                className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
              >
                <div className="pt-4">
                  <CompanyFormSection />
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}
        </div>
      </div>
    </section>
  );
};

export default FormSection;
