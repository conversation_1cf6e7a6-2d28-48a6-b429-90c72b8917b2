import { Helmet } from 'react-helmet-async';
import { useI18n } from '@/contexts/I18nContext';
import { useLocation } from 'react-router-dom';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
  canonical?: string;
  alternateLanguages?: { [key: string]: string };
  structuredData?: object;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  image = '/logo.png',
  type = 'website',
  noindex = false,
  canonical,
  alternateLanguages,
  structuredData
}) => {
  const { language } = useI18n();
  const location = useLocation();
  
  const baseUrl = 'https://sohnus.com'; // Replace with actual domain
  const currentUrl = `${baseUrl}${location.pathname}`;
  const canonicalUrl = canonical || currentUrl;
  
  // Default meta data based on language and page
  const getDefaultTitle = () => {
    if (location.pathname === '/hiring') {
      switch (language) {
        case 'de':
          return 'Sohnus - KI-gestützte Recruiting-Plattform für Arbeitgeber';
        case 'zh':
          return 'Sohnus - 面向雇主的AI招聘平台';
        default:
          return 'Sohnus - AI-Powered Hiring Platform for Employers';
      }
    } else {
      switch (language) {
        case 'de':
          return 'Sohnus - KI-gestützte Jobplattform für Arbeitnehmer';
        case 'zh':
          return 'Sohnus - 面向求职者的AI求职平台';
        default:
          return 'Sohnus - AI-Powered Job Platform for Workers';
      }
    }
  };

  const getDefaultDescription = () => {
    if (location.pathname === '/hiring') {
      switch (language) {
        case 'de':
          return 'Finden Sie qualifizierte Mitarbeiter mit unserer KI-gestützten Recruiting-Plattform. Effiziente Personalsuche für deutsche Unternehmen.';
        case 'zh':
          return '使用我们的AI招聘平台找到合格的员工。为德国公司提供高效的人才招聘服务。';
        default:
          return 'Find qualified employees with our AI-powered recruiting platform. Efficient talent acquisition for German companies.';
      }
    } else {
      switch (language) {
        case 'de':
          return 'Finden Sie Ihren Traumjob in Deutschland mit unserer KI-gestützten Jobplattform. Intelligente Jobvermittlung für Arbeitnehmer.';
        case 'zh':
          return '使用我们的AI求职平台在德国找到您的理想工作。为求职者提供智能工作匹配服务。';
        default:
          return 'Find your dream job in Germany with our AI-powered job platform. Intelligent job matching for workers.';
      }
    }
  };

  const getDefaultKeywords = () => {
    if (location.pathname === '/hiring') {
      switch (language) {
        case 'de':
          return 'Recruiting Deutschland, Personalsuche, Mitarbeiter finden, KI Recruiting, Arbeitgeber, Personalvermittlung, Talentakquise, HR Deutschland, Stellenausschreibung, Bewerbersuche';
        case 'zh':
          return '德国招聘, 人才招聘, 员工招聘, AI招聘, 德国雇主, 人力资源, 招聘平台, 德国工作, 人才获取, 招聘服务';
        default:
          return 'recruiting Germany, hiring platform, talent acquisition, AI recruiting, employers Germany, human resources, job posting, candidate search, recruitment services, German companies';
      }
    } else {
      switch (language) {
        case 'de':
          return 'Jobs Deutschland, Stellensuche, Arbeit finden, KI Jobsuche, Arbeitnehmer, Karriere Deutschland, Jobvermittlung, Arbeitsplatz, Stellenangebote, Berufschancen';
        case 'zh':
          return '德国工作, 德国求职, 找工作, AI求职, 德国求职者, 职业发展, 工作机会, 德国就业, 求职平台, 职业匹配';
        default:
          return 'jobs Germany, job search Germany, find work Germany, AI job search, workers Germany, career opportunities, employment Germany, job matching, work opportunities, German job market';
      }
    }
  };

  const finalTitle = title || getDefaultTitle();
  const finalDescription = description || getDefaultDescription();
  const finalKeywords = keywords || getDefaultKeywords();
  const imageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <html lang={language} />
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content="Sohnus" />
      <meta name="robots" content={noindex ? 'noindex,nofollow' : 'index,follow'} />
      <meta name="language" content={language === 'de' ? 'German' : language === 'zh' ? 'Chinese' : 'English'} />
      <meta name="geo.region" content="DE" />
      <meta name="geo.country" content="Germany" />
      <meta name="geo.placename" content="Germany" />
      <meta name="target-country" content="DE" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />

      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content={finalTitle} />
      <meta property="og:site_name" content="Sohnus" />
      <meta property="og:locale" content={language === 'de' ? 'de_DE' : language === 'zh' ? 'zh_CN' : 'en_US'} />
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={imageUrl} />
      <meta name="twitter:image:alt" content={finalTitle} />
      
      {/* Hreflang Tags */}
      {alternateLanguages && Object.entries(alternateLanguages).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}

      {/* Default hreflang for current page with proper locale codes */}
      <link rel="alternate" hrefLang="en-US" href={`${baseUrl}${location.pathname}`} />
      <link rel="alternate" hrefLang="de-DE" href={`${baseUrl}${location.pathname}`} />
      <link rel="alternate" hrefLang="zh-CN" href={`${baseUrl}${location.pathname}`} />
      <link rel="alternate" hrefLang="x-default" href={`${baseUrl}${location.pathname}`} />

      {/* Additional regional targeting */}
      <link rel="alternate" hrefLang="en" href={`${baseUrl}${location.pathname}`} />
      <link rel="alternate" hrefLang="de" href={`${baseUrl}${location.pathname}`} />
      <link rel="alternate" hrefLang="zh" href={`${baseUrl}${location.pathname}`} />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;
