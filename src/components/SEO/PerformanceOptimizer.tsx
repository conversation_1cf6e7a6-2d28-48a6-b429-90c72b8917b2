import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const PerformanceOptimizer = () => {
  const location = useLocation();

  useEffect(() => {
    // Preload critical resources based on current page
    const preloadCriticalResources = () => {
      // Preload fonts
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.as = 'font';
      fontLink.type = 'font/woff2';
      fontLink.crossOrigin = 'anonymous';
      fontLink.href = '/fonts/inter-var.woff2'; // Adjust based on your font
      document.head.appendChild(fontLink);

      // Preload critical images based on page
      const criticalImages = [
        '/logo.png',
        '/modal-image.svg'
      ];

      if (location.pathname === '/') {
        criticalImages.push('/lovable-uploads/3db855a8-1785-4766-9299-1c6033715fe4.png');
      }

      criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });
    };

    // Lazy load non-critical resources
    const lazyLoadResources = () => {
      // Lazy load feature images
      const featureImages = [
        '/feature-1.png',
        '/feature-2.png',
        '/feature-3.png'
      ];

      featureImages.forEach(src => {
        const img = new Image();
        img.loading = 'lazy';
        img.src = src;
      });
    };

    // Optimize third-party scripts
    const optimizeThirdPartyScripts = () => {
      // Delay non-critical scripts
      setTimeout(() => {
        // Add any non-critical third-party scripts here
      }, 3000);
    };

    // Run optimizations
    preloadCriticalResources();
    lazyLoadResources();
    optimizeThirdPartyScripts();

    // Cleanup function
    return () => {
      // Remove preload links if needed
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      preloadLinks.forEach(link => {
        if (link.getAttribute('href')?.includes('/fonts/') || 
            link.getAttribute('href')?.includes('/images/')) {
          link.remove();
        }
      });
    };
  }, [location.pathname]);

  // Add critical CSS for above-the-fold content
  useEffect(() => {
    const criticalCSS = `
      /* Critical CSS for above-the-fold content */
      .hero-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
      }
      
      .hero-title {
        font-size: clamp(2rem, 5vw, 4rem);
        line-height: 1.2;
        font-weight: 700;
      }
      
      .hero-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.25rem);
        line-height: 1.6;
        margin-top: 1rem;
      }
      
      /* Prevent layout shift */
      .modal-backdrop {
        backdrop-filter: blur(4px);
      }
      
      .floating-button {
        will-change: transform;
        transform: translateZ(0);
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    style.id = 'critical-css';
    document.head.appendChild(style);

    return () => {
      const existingStyle = document.getElementById('critical-css');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  // Implement resource hints
  useEffect(() => {
    // Add resource hints for better performance
    const resourceHints = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
      { rel: 'dns-prefetch', href: '//www.googletagmanager.com' },
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' }
    ];

    resourceHints.forEach(hint => {
      const existingLink = document.querySelector(`link[rel="${hint.rel}"][href="${hint.href}"]`);
      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = hint.rel;
        link.href = hint.href;
        if (hint.crossOrigin) {
          link.crossOrigin = hint.crossOrigin;
        }
        document.head.appendChild(link);
      }
    });
  }, []);

  return null; // This component doesn't render anything
};

export default PerformanceOptimizer;
