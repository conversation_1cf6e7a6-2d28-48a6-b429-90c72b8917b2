# Netlify SPA routing configuration
# This ensures all routes are handled by React Router client-side

# Handle all routes by serving index.html
/*    /index.html   200

# Specific redirects for common patterns
/hiring/*    /index.html   200
/privacy-policy    /index.html   200
/privacy-policy-de    /index.html   200
/terms-of-service    /index.html   200
/terms-of-service-de    /index.html   200

# API routes (if any) should be handled before the catch-all
# /api/*    /api/:splat   200

# Static assets should be served directly
/assets/*    /assets/:splat   200
/locales/*    /locales/:splat   200
/team/*    /team/:splat   200
