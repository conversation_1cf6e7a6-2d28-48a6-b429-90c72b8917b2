<svg width="400" height="225" viewBox="0 0 400 225" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with job seeker theme gradient -->
  <rect width="400" height="225" fill="url(#jobSeekerGradient)"/>
  
  <!-- Subtle pattern overlay -->
  <rect width="400" height="225" fill="url(#pattern)" opacity="0.1"/>
  
  <!-- Central icon -->
  <g transform="translate(175, 87.5)">
    <!-- Document/Article icon -->
    <rect x="0" y="0" width="50" height="50" rx="4" fill="white" opacity="0.9"/>
    <rect x="8" y="8" width="34" height="2" rx="1" fill="#F59E0B"/>
    <rect x="8" y="16" width="26" height="2" rx="1" fill="#F59E0B" opacity="0.7"/>
    <rect x="8" y="24" width="30" height="2" rx="1" fill="#F59E0B" opacity="0.7"/>
    <rect x="8" y="32" width="20" height="2" rx="1" fill="#F59E0B" opacity="0.5"/>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="50" r="20" fill="white" opacity="0.1"/>
  <circle cx="320" cy="180" r="15" fill="white" opacity="0.1"/>
  <circle cx="50" cy="180" r="12" fill="white" opacity="0.1"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="jobSeekerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:0.6" />
    </linearGradient>
    
    <pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="white" opacity="0.3"/>
    </pattern>
  </defs>
</svg>
