<svg width="400" height="225" viewBox="0 0 400 225" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with employer theme gradient -->
  <rect width="400" height="225" fill="url(#employerGradient)"/>
  
  <!-- Subtle pattern overlay -->
  <rect width="400" height="225" fill="url(#pattern)" opacity="0.1"/>
  
  <!-- Central icon -->
  <g transform="translate(175, 87.5)">
    <!-- Document/Article icon -->
    <rect x="0" y="0" width="50" height="50" rx="4" fill="white" opacity="0.9"/>
    <rect x="8" y="8" width="34" height="2" rx="1" fill="#0EA5E9"/>
    <rect x="8" y="16" width="26" height="2" rx="1" fill="#0EA5E9" opacity="0.7"/>
    <rect x="8" y="24" width="30" height="2" rx="1" fill="#0EA5E9" opacity="0.7"/>
    <rect x="8" y="32" width="20" height="2" rx="1" fill="#0EA5E9" opacity="0.5"/>
  </g>
  
  <!-- Decorative elements -->
  <rect x="80" y="40" width="15" height="15" rx="2" fill="white" opacity="0.1" transform="rotate(45 87.5 47.5)"/>
  <rect x="300" y="160" width="12" height="12" rx="2" fill="white" opacity="0.1" transform="rotate(45 306 166)"/>
  <rect x="40" y="170" width="10" height="10" rx="2" fill="white" opacity="0.1" transform="rotate(45 45 175)"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="employerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0EA5E9;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:0.6" />
    </linearGradient>
    
    <pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect x="9" y="9" width="2" height="2" fill="white" opacity="0.3"/>
    </pattern>
  </defs>
</svg>
