const fs = require('fs');
const path = require('path');

const basePath = path.resolve(__dirname, 'public', 'locales');// 修改为你自己的路径

// 你要添加或覆盖的 key
const targetKey = 'hero.employer.subtitle';

// 各语言要设置的文本
const translations = {
  en: "Connect with 1,000+ verified blue‑collar professionals in your area, with our dedicated support every step of the way.",
  de: "Finden Sie über 1.000 verifizierte Fachkräfte in Ihrer Nähe – mit persönlicher Unterstützung von uns in jedem Schritt.",
  zh: "与您所在地区 1,000 多位经过验证的蓝领工人建立联系，我们将在每一步提供贴心支持。",
  es: "Conéctate con más de 1.000 profesionales cualificados en tu zona, con nuestro apoyo personalizado en cada paso del proceso."
};

// 把 'features.title' 拆成 ['features', 'title']
function setDeep(obj, keys, value) {
  const lastKey = keys.pop();
  let pointer = obj;
  for (const key of keys) {
    if (!pointer[key] || typeof pointer[key] !== 'object') {
      pointer[key] = {};
    }
    pointer = pointer[key];
  }
  pointer[lastKey] = value;
}

Object.entries(translations).forEach(([lang, text]) => {
  const filePath = path.join(basePath, `${lang}.json`);
  if (!fs.existsSync(filePath)) {
    console.warn(`❗ File not found: ${filePath}`);
    return;
  }

  const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
  setDeep(content, targetKey.split('.'), text);
  fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf-8');
  console.log(`✅ Updated ${lang}.json`);
});
